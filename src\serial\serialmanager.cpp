#include "serialmanager.h"
#include <QMutexLocker>
#include "log.h"

SerialManager::SerialManager(QObject *parent)
    : QObject(parent)
    , m_serialDevice(nullptr)
    , m_atParser(nullptr)
    , m_reconnectTimer(nullptr)
    , m_autoReconnect(false)
    , m_reconnectInterval(5000)
    , m_isInitialized(false)
{
    m_serialDevice = new SerialDevice(this);
    m_atParser = new AtCommandParser(this);
    m_reconnectTimer = new QTimer(this);
    m_reconnectTimer->setSingleShot(true);

    // 连接串口设备信号
    connect(m_serialDevice, &SerialDevice::dataReceived,
            this, &SerialManager::onSerialDataReceived);
    connect(m_serialDevice, &SerialDevice::connectionChanged,
            this, &SerialManager::onSerialConnectionChanged);
    connect(m_serialDevice, &SerialDevice::errorOccurred,
            this, &SerialManager::onSerialError);

    // 连接AT解析器信号
    connect(m_atParser, &AtCommandParser::commandParsed,
            this, &SerialManager::onAtCommandParsed);
    connect(m_atParser, &AtCommandParser::responseParsed,
            this, &SerialManager::onAtResponseParsed);
    connect(m_atParser, &AtCommandParser::urcReceived,
            this, &SerialManager::onUrcReceived);
    connect(m_atParser, &AtCommandParser::parseError,
            this, &SerialManager::onAtParseError);

    // 连接重连定时器
    connect(m_reconnectTimer, &QTimer::timeout,
            this, &SerialManager::onReconnectTimeout);
}

SerialManager::~SerialManager()
{
    stop();
}

bool SerialManager::initialize(const SerialConfig& config)
{
    QMutexLocker locker(&m_mutex);

    m_config = config;

    // 配置串口设备
    bool success = m_serialDevice->setPortSettings(
        config.portName,
        config.baudRate,
        config.dataBits,
        config.parity,
        config.stopBits,
        config.flowControl
    );

    if (!success) {
        setLastError("串口参数设置失败");
        return false;
    }

    // 设置超时时间
    m_serialDevice->setTimeout(config.readTimeout, config.writeTimeout);

    // 配置AT解析器
    m_atParser->setResponseTimeout(config.readTimeout);

    m_isInitialized = true;

    logInfo(QString("串口管理器初始化成功: %1").arg(config.portName));
    return true;
}

bool SerialManager::start()
{
    QMutexLocker locker(&m_mutex);

    if (!m_isInitialized) {
        setLastError("串口管理器未初始化");
        return false;
    }

    if (m_serialDevice->isOpen()) {
        return true; // 已经启动
    }

    if (!m_serialDevice->open()) {
        setLastError(QString("无法打开串口: %1").arg(m_serialDevice->lastError()));

        // 启用自动重连
        if (m_autoReconnect) {
            m_reconnectTimer->start(m_reconnectInterval);
        }

        return false;
    }

    logInfo("串口管理器启动成功");
    return true;
}

void SerialManager::stop()
{
    QMutexLocker locker(&m_mutex);

    if (m_reconnectTimer && m_reconnectTimer->isActive()) {
        m_reconnectTimer->stop();
    }

    if (m_serialDevice && m_serialDevice->isOpen()) {
        m_serialDevice->close();
    }

    if (m_atParser) {
        m_atParser->clearBuffer();
    }

    logInfo("串口管理器已停止");
}

bool SerialManager::isConnected() const
{
    QMutexLocker locker(&m_mutex);
    return m_serialDevice && m_serialDevice->isOpen();
}

bool SerialManager::sendAtCommand(const AtCommand& command)
{
    QMutexLocker locker(&m_mutex);

    if (!isConnected()) {
        setLastError("串口未连接");
        return false;
    }

    if (!command.isValid()) {
        setLastError("无效的AT指令");
        return false;
    }

    QString formattedCommand = m_atParser->formatCommand(command);
    QByteArray data = formattedCommand.toUtf8();

    qint64 bytesWritten = m_serialDevice->write(data);
    if (bytesWritten == -1) {
        setLastError(QString("发送AT指令失败: %1").arg(m_serialDevice->lastError()));
        return false;
    }

    logInfo(QString("发送AT指令: %1").arg(command.format()));
    emit atCommandSent(command);
    emit rawDataSent(data);

    return true;
}

bool SerialManager::sendAtCommand(const QString& commandStr)
{
    AtCommand command = m_atParser->parseCommand(commandStr);
    if (!command.isValid()) {
        setLastError(QString("无法解析AT指令: %1").arg(commandStr));
        return false;
    }

    return sendAtCommand(command);
}

bool SerialManager::sendRawData(const QByteArray& data)
{
    QMutexLocker locker(&m_mutex);

    if (!isConnected()) {
        setLastError("串口未连接");
        return false;
    }

    if (data.isEmpty()) {
        return true;
    }

    qint64 bytesWritten = m_serialDevice->write(data);
    if (bytesWritten == -1) {
        setLastError(QString("发送原始数据失败: %1").arg(m_serialDevice->lastError()));
        return false;
    }

    logDebug(QString("发送原始数据: %1 字节数: %2").arg(QString(data.toHex(' '))).arg(bytesWritten));
    emit rawDataSent(data);

    return true;
}

ISerialDevice* SerialManager::getSerialDevice() const
{
    return m_serialDevice;
}

AtCommandParser* SerialManager::getAtParser() const
{
    return m_atParser;
}

QString SerialManager::lastError() const
{
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

void SerialManager::setAutoReconnect(bool enabled, int interval)
{
    QMutexLocker locker(&m_mutex);
    m_autoReconnect = enabled;
    m_reconnectInterval = interval;

    logInfo(QString("自动重连设置: %1 间隔: %2ms").arg(enabled ? "启用" : "禁用").arg(interval));
}

QStringList SerialManager::getAvailablePorts()
{
    return SerialDevice::availablePorts();
}

void SerialManager::onSerialDataReceived(const QByteArray& data)
{
    // 将接收到的数据传递给AT解析器
    m_atParser->processReceivedData(data);

    // 发出原始数据接收信号
    emit rawDataReceived(data);
}

void SerialManager::onSerialConnectionChanged(bool connected)
{
    logInfo(QString("串口连接状态变化: %1").arg(connected ? "已连接" : "已断开"));

    if (!connected && m_autoReconnect) {
        // 启动重连定时器
        m_reconnectTimer->start(m_reconnectInterval);
    }

    emit connectionChanged(connected);
}

void SerialManager::onSerialError(const QString& error)
{
    setLastError(QString("串口错误: %1").arg(error));
    logError(m_lastError);
    emit errorOccurred(m_lastError);
}

void SerialManager::onAtCommandParsed(const AtCommand& command)
{
    logDebug(QString("解析到AT指令: %1").arg(command.format()));
    // 这里通常是从串口接收到的指令，可能需要特殊处理
    // 在桥接模式下，这种情况较少见
}

void SerialManager::onAtResponseParsed(const AtResponse& response)
{
    logDebug(QString("解析到AT响应: %1").arg(response.format()));
    emit atResponseReceived(response);
}

void SerialManager::onUrcReceived(const AtResponse& response)
{
    logInfo(QString("接收到URC: %1").arg(response.format()));
    emit urcReceived(response);
}

void SerialManager::onAtParseError(const QString& error)
{
    setLastError(QString("AT解析错误: %1").arg(error));
    logWarnning(m_lastError);
    emit errorOccurred(m_lastError);
}

void SerialManager::onReconnectTimeout()
{
    logInfo("尝试自动重连串口...");
    attemptReconnect();
}

void SerialManager::setLastError(const QString& error)
{
    m_lastError = error;
}

void SerialManager::attemptReconnect()
{
    if (!m_autoReconnect) {
        return;
    }

    if (m_serialDevice->isOpen()) {
        return; // 已经连接
    }

    logInfo(QString("尝试重连串口: %1").arg(m_config.portName));

    if (m_serialDevice->open()) {
        logInfo("串口重连成功");
    } else {
        logWarnning(QString("串口重连失败: %1").arg(m_serialDevice->lastError()));
        // 继续重连
        m_reconnectTimer->start(m_reconnectInterval);
    }
}
